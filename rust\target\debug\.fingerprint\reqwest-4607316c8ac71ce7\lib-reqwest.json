{"rustc": 12488743700189009532, "features": "[\"__tls\", \"blocking\", \"default-tls\", \"json\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 10900257523021023328, "path": 15257297935374822728, "deps": [[40386456601120721, "percent_encoding", false, 11201700476752682152], [95042085696191081, "ipnet", false, 10915975857295199336], [778154619793643451, "hyper_util", false, 8080558029861480249], [784494742817713399, "tower_service", false, 14713849103421297527], [1811549171721445101, "futures_channel", false, 15858569383539872527], [1906322745568073236, "pin_project_lite", false, 15636383175531162806], [2054153378684941554, "tower_http", false, 6260865936528477626], [2517136641825875337, "sync_wrapper", false, 13923617165680171792], [2883436298747778685, "rustls_pki_types", false, 7812709075471354703], [3150220818285335163, "url", false, 12851303187790047985], [3722963349756955755, "once_cell", false, 15071223915464446217], [5695049318159433696, "tower", false, 9689093134416761524], [5986029879202738730, "log", false, 5638320630720732856], [7620660491849607393, "futures_core", false, 11290189045161448346], [9010263965687315507, "http", false, 14438606696231830319], [9538054652646069845, "tokio", false, 15305522293980401147], [9689903380558560274, "serde", false, 10977844928489340561], [10229185211513642314, "mime", false, 799632151728794554], [10629569228670356391, "futures_util", false, 8240521222716982159], [11957360342995674422, "hyper", false, 606221830337534857], [12186126227181294540, "tokio_native_tls", false, 17787011813262349703], [13077212702700853852, "base64", false, 13497128256263695626], [14084095096285906100, "http_body", false, 7124342440318254419], [15367738274754116744, "serde_json", false, 17974425630600201351], [16066129441945555748, "bytes", false, 4498305474353292168], [16542808166767769916, "serde_urlencoded", false, 5617567085289434712], [16785601910559813697, "native_tls_crate", false, 2198943927764683685], [16900715236047033623, "http_body_util", false, 15475171450182443256], [18273243456331255970, "hyper_tls", false, 16806482379076350031]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-4607316c8ac71ce7\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}