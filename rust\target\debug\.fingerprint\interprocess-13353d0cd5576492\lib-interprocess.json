{"rustc": 12488743700189009532, "features": "[\"blocking\", \"default\", \"futures-core\", \"futures-io\", \"intmap\", \"nonblocking\", \"once_cell\", \"signals\", \"spinning\", \"thiserror\"]", "declared_features": "[\"blocking\", \"default\", \"doc_cfg\", \"futures-core\", \"futures-io\", \"intmap\", \"nonblocking\", \"once_cell\", \"signals\", \"spinning\", \"thiserror\", \"tokio\", \"tokio_support\"]", "target": 15901425574398377358, "profile": 8276155916380437441, "path": 10479453526534412440, "deps": [[5103565458935487, "futures_io", false, 3715525439416892189], [413895055751620470, "to_method", false, 17437964061919521163], [2924422107542798392, "libc", false, 1728529705282482843], [3722963349756955755, "once_cell", false, 15071223915464446217], [7620660491849607393, "futures_core", false, 11290189045161448346], [8008191657135824715, "thiserror", false, 1613777258667004616], [10020888071089587331, "<PERSON>ap<PERSON>", false, 4646730283605844445], [10411997081178400487, "cfg_if", false, 1317454610972216709], [12944474953899598757, "blocking", false, 10586209452383263575], [13564245402892897462, "spinning", false, 9872747983002128191], [15344998369347094209, "build_script_build", false, 8451146113959390248], [17329584784123403638, "intmap", false, 6090208609847298396]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\interprocess-13353d0cd5576492\\dep-lib-interprocess", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}