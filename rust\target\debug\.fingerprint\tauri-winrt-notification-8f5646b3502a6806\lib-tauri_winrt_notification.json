{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 8276155916380437441, "path": 5451450293158687294, "deps": [[1462335029370885857, "quick_xml", false, 7100368560789402350], [3334271191048661305, "windows_version", false, 3941034028027633390], [10806645703491011684, "thiserror", false, 15351999382079023931], [13116089016666501665, "windows", false, 8533934300696822599]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-8f5646b3502a6806\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}