{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 288010345193536171, "profile": 8276155916380437441, "path": 3728461599029596206, "deps": [[2924422107542798392, "libc", false, 1728529705282482843], [10020888071089587331, "<PERSON>ap<PERSON>", false, 4646730283605844445], [16244578264353215491, "build_script_build", false, 9528942542528738809]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\findshlibs-07389035e3c64935\\dep-lib-findshlibs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}