/// The rustc version that was used to compile this crate.
pub const RUSTC_VERSION: Option<&'static str> = Some("1.85.1");
/// The rustc release channel that was used to compile this crate.
pub const RUSTC_CHANNEL: Option<&'static str> = Some("stable");
/// The platform identifier.
#[allow(unused)] pub const PLATFORM: &str = "windows";
/// The CPU architecture identifier.
pub const ARCH: &str = "x86_64";
