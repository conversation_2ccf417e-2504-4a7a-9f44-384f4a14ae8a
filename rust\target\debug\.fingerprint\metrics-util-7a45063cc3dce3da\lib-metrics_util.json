{"rustc": 12488743700189009532, "features": "[\"crossbeam-epoch\", \"crossbeam-utils\", \"handles\", \"hashbrown\", \"num_cpus\", \"quanta\", \"recency\", \"registry\", \"sketches-ddsketch\", \"summary\"]", "declared_features": "[\"ahash\", \"aho-corasick\", \"crossbeam-epoch\", \"crossbeam-utils\", \"debugging\", \"default\", \"handles\", \"hashbrown\", \"indexmap\", \"layer-filter\", \"layer-router\", \"layers\", \"num_cpus\", \"ordered-float\", \"quanta\", \"radix_trie\", \"recency\", \"registry\", \"sketches-ddsketch\", \"summary\"]", "target": 13028150694916642802, "profile": 8276155916380437441, "path": 14924716181628827192, "deps": [[2357570525450087091, "num_cpus", false, 6942597930238747902], [3088994813707213335, "quanta", false, 6068439543086337821], [3528074118530651198, "crossbeam_epoch", false, 15512171165049938070], [4468123440088164316, "crossbeam_utils", false, 3071824388060468787], [4801984952432540513, "metrics", false, 6138362038860879135], [13018563866916002725, "hashbrown", false, 6315502032586536327], [16687810213102005944, "sketches_ddsketch", false, 18364573874239708322]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\metrics-util-7a45063cc3dce3da\\dep-lib-metrics_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}