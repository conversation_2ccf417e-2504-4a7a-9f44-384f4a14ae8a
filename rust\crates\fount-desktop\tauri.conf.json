{"build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devUrl": "../../../static", "frontendDist": "../../../static", "withGlobalTauri": false}, "package": {"productName": "Fount", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "dialog": {"all": false, "open": true, "save": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "createDir": true, "removeDir": true, "removeFile": true, "exists": true}, "notification": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.fount.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "Fount", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}}