{"rustc": 12488743700189009532, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 3992724396554112236, "path": 15760570039277490425, "deps": [[1906322745568073236, "pin_project_lite", false, 15636383175531162806], [2967683870285097694, "tracing_attributes", false, 15654068158775397850], [5986029879202738730, "log", false, 7320443587293871305], [11033263105862272874, "tracing_core", false, 10485554372642425387]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-1df7ccd7b27c3f01\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}