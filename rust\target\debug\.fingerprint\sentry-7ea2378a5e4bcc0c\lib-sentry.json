{"rustc": 12488743700189009532, "features": "[\"backtrace\", \"contexts\", \"debug-images\", \"default\", \"httpdate\", \"native-tls\", \"panic\", \"reqwest\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-panic\", \"sentry-tracing\", \"tokio\", \"tracing\", \"transport\"]", "declared_features": "[\"UNSTABLE_metrics\", \"anyhow\", \"backtrace\", \"contexts\", \"curl\", \"debug-images\", \"debug-logs\", \"default\", \"http-client\", \"httpdate\", \"isahc\", \"log\", \"native-tls\", \"panic\", \"reqwest\", \"rustls\", \"sentry-anyhow\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-log\", \"sentry-panic\", \"sentry-slog\", \"sentry-tower\", \"sentry-tracing\", \"serde_json\", \"slog\", \"surf\", \"surf-h1\", \"test\", \"tokio\", \"tower\", \"tower-axum-matched-path\", \"tower-http\", \"tracing\", \"transport\", \"ureq\", \"webpki-roots\"]", "target": 1678334649273565773, "profile": 8276155916380437441, "path": 5709427884395948961, "deps": [[889364689474263285, "reqwest", false, 5896923511524638513], [994385991029174609, "sentry_tracing", false, 13355217342797912077], [6304235478050270880, "httpdate", false, 15907081395996601169], [6564039303567649966, "sentry_core", false, 2118961345323553881], [9538054652646069845, "tokio", false, 15305522293980401147], [11308626530065046827, "sentry_panic", false, 13778644763025444868], [13930065473154359735, "sentry_debug_images", false, 8022569382150766796], [14452548244456819072, "sentry_backtrace", false, 10233455358096462606], [16785601910559813697, "native_tls", false, 2198943927764683685], [17745100799483310686, "sentry_contexts", false, 14451800231207285542]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sentry-7ea2378a5e4bcc0c\\dep-lib-sentry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}