//! IPC服务器模块
//! 对应原文件: src/server/ipc_server.mjs

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};
use interprocess::local_socket::{LocalSocketListener, LocalSocketStream};
use std::io::{Read, Write};

/// IPC命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcCommand {
    pub command: String,
    pub args: serde_json::Value,
    pub id: Option<String>,
}

/// IPC响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcResponse {
    pub success: bool,
    pub data: Option<serde_json::Value>,
    pub error: Option<String>,
    pub id: Option<String>,
}

/// IPC管理器 (对应原文件的 IPCManager)
pub struct IpcManager {
    listeners: Arc<RwLock<Vec<LocalSocketListener>>>,
    handlers: Arc<RwLock<HashMap<String, Box<dyn IpcHandler + Send + Sync>>>>,
}

/// IPC处理器trait
#[async_trait::async_trait]
pub trait IpcHandler {
    async fn handle(&self, command: &str, args: serde_json::Value) -> Result<serde_json::Value>;
}

impl IpcManager {
    /// 创建新的IPC管理器
    pub fn new() -> Self {
        Self {
            listeners: Arc::new(RwLock::new(Vec::new())),
            handlers: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 启动IPC服务器 (对应原文件的 startServer)
    pub async fn start_server(&self) -> Result<bool> {
        info!("Starting IPC server");
        
        // 在Windows上使用命名管道，在Unix上使用Unix域套接字
        let socket_name = if cfg!(windows) {
            r"\\.\pipe\fount_ipc"
        } else {
            "/tmp/fount_ipc.sock"
        };
        
        // 清理可能存在的旧套接字文件
        #[cfg(unix)]
        {
            let _ = std::fs::remove_file(socket_name);
        }
        
        match LocalSocketListener::bind(socket_name.to_string()) {
            Ok(listener) => {
                info!("IPC server listening on {}", socket_name);
                
                // 启动监听任务
                let handlers = self.handlers.clone();
                tokio::spawn(async move {
                    loop {
                        match listener.accept() {
                            Ok(stream) => {
                                let handlers = handlers.clone();
                                tokio::spawn(async move {
                                    if let Err(e) = Self::handle_client(stream, handlers).await {
                                        error!("IPC client error: {}", e);
                                    }
                                });
                            }
                            Err(e) => {
                                error!("IPC accept error: {}", e);
                                break;
                            }
                        }
                    }
                });
                
                Ok(true)
            }
            Err(e) => {
                warn!("Failed to start IPC server: {}", e);
                Ok(false)
            }
        }
    }
    
    /// 处理客户端连接
    async fn handle_client(
        mut stream: LocalSocketStream,
        handlers: Arc<RwLock<HashMap<String, Box<dyn IpcHandler + Send + Sync>>>>,
    ) -> Result<()> {
        let mut buffer = vec![0; 4096];
        
        loop {
            match stream.read(&mut buffer) {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    let data = &buffer[..n];
                    let command: IpcCommand = serde_json::from_slice(data)?;

                    debug!("Received IPC command: {}", command.command);

                    let response = {
                        let handlers = handlers.read().await;
                        if let Some(handler) = handlers.get(&command.command) {
                            match handler.handle(&command.command, command.args).await {
                                Ok(data) => IpcResponse {
                                    success: true,
                                    data: Some(data),
                                    error: None,
                                    id: command.id,
                                },
                                Err(e) => IpcResponse {
                                    success: false,
                                    data: None,
                                    error: Some(e.to_string()),
                                    id: command.id,
                                },
                            }
                        } else {
                            IpcResponse {
                                success: false,
                                data: None,
                                error: Some(format!("Unknown command: {}", command.command)),
                                id: command.id,
                            }
                        }
                    };

                    let response_data = serde_json::to_vec(&response)?;
                    stream.write_all(&response_data)?;
                }
                Err(e) => {
                    error!("IPC read error: {}", e);
                    break;
                }
            }
        }
        
        Ok(())
    }
    
    /// 注册命令处理器
    pub async fn register_handler(&self, command: String, handler: Box<dyn IpcHandler + Send + Sync>) {
        let mut handlers = self.handlers.write().await;
        handlers.insert(command, handler);
    }
    
    /// 停止IPC服务器
    pub async fn stop_server(&self) {
        info!("Stopping IPC server");
        // 清理资源
        let mut listeners = self.listeners.write().await;
        listeners.clear();
    }
}

impl Default for IpcManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 默认IPC处理器
pub struct DefaultIpcHandler;

#[async_trait::async_trait]
impl IpcHandler for DefaultIpcHandler {
    async fn handle(&self, command: &str, args: serde_json::Value) -> Result<serde_json::Value> {
        match command {
            "ping" => Ok(serde_json::json!({"message": "pong"})),
            "echo" => Ok(args),
            _ => Err(anyhow::anyhow!("Unknown command: {}", command)),
        }
    }
}

/// 处理IPC命令 (对应原文件的 processIPCCommand)
pub async fn process_ipc_command(command: &str, args: serde_json::Value) -> Result<serde_json::Value> {
    // 简化的命令处理
    match command {
        "runshell" => {
            info!("Processing runshell command: {:?}", args);
            Ok(serde_json::json!({"status": "success", "message": "Shell command processed"}))
        }
        _ => {
            warn!("Unknown IPC command: {}", command);
            Err(anyhow::anyhow!("Unknown command: {}", command))
        }
    }
}

/// 全局IPC管理器实例
static GLOBAL_IPC_MANAGER: std::sync::OnceLock<IpcManager> = std::sync::OnceLock::new();

/// 获取全局IPC管理器
pub fn get_ipc_manager() -> &'static IpcManager {
    GLOBAL_IPC_MANAGER.get_or_init(IpcManager::new)
}
