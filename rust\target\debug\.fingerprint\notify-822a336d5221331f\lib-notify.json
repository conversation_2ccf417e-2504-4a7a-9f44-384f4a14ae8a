{"rustc": 12488743700189009532, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 8276155916380437441, "path": 13842500022749034100, "deps": [[1999565553139417705, "windows_sys", false, 12098412913978163471], [2924422107542798392, "libc", false, 1728529705282482843], [3869670940427635694, "filetime", false, 12786325323371219132], [5986029879202738730, "log", false, 5638320630720732856], [9727213718512686088, "crossbeam_channel", false, 17257937359551305191], [15622660310229662834, "walkdir", false, 5924314621476732747]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-822a336d5221331f\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}