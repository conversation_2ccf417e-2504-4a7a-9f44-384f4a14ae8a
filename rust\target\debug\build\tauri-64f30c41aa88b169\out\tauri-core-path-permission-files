["\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\path\\autogenerated\\default.toml"]