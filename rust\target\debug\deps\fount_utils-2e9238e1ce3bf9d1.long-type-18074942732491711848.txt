hashbrown::map::HashMap<TypeId, Box<(dyn std::any::Any + 'static)>, rustc_hash::FxBuildHasher>
HashMap<TypeId, Box<(dyn std::any::Any + 'static)>, rustc_hash::FxBuildHasher>
std::option::Option<HashMap<TypeId, Box<(dyn std::any::Any + 'static)>, rustc_hash::FxBuildHasher>>
(std::string::String, FluentBundle<FluentResource, intl_memoizer::IntlLangMemoizer>)
hashbrown::raw::RawTable<(std::string::String, FluentBundle<FluentResource, intl_memoizer::IntlLangMemoizer>)>
hashbrown::map::HashMap<std::string::String, FluentBundle<FluentResource, intl_memoizer::IntlLangMemoizer>, RandomState>
HashMap<std::string::String, FluentBundle<FluentResource, intl_memoizer::IntlLangMemoizer>>
