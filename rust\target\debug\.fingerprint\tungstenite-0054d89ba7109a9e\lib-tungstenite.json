{"rustc": 12488743700189009532, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 3965174974797606104, "profile": 8276155916380437441, "path": 14836315071677455137, "deps": [[99287295355353247, "data_encoding", false, 4470832849226549743], [3712811570531045576, "byteorder", false, 7621920514671445394], [4359956005902820838, "utf8", false, 1890257754215267468], [5986029879202738730, "log", false, 7320443587293871305], [6163892036024256188, "httparse", false, 6513620970661446744], [8008191657135824715, "thiserror", false, 1613777258667004616], [9010263965687315507, "http", false, 14438606696231830319], [10724389056617919257, "sha1", false, 5307172773621335540], [13208667028893622512, "rand", false, 9557639272206221063], [16066129441945555748, "bytes", false, 4498305474353292168]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-0054d89ba7109a9e\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}