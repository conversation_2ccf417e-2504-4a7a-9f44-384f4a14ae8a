["\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\tray\\autogenerated\\default.toml"]