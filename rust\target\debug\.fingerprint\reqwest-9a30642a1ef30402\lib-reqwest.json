{"rustc": 12488743700189009532, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"gzip\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-util\", \"wasm-streams\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 8276155916380437441, "path": 7565996450976943501, "deps": [[40386456601120721, "percent_encoding", false, 11201700476752682152], [95042085696191081, "ipnet", false, 10915975857295199336], [126872836426101300, "async_compression", false, 7193909801330957166], [264090853244900308, "sync_wrapper", false, 1343538870603680152], [784494742817713399, "tower_service", false, 14713849103421297527], [1044435446100926395, "hyper_rustls", false, 5713352843041104285], [1288403060204016458, "tokio_util", false, 9453835930420776786], [1906322745568073236, "pin_project_lite", false, 15636383175531162806], [2779053297469913730, "cookie_crate", false, 15121680975376495304], [3150220818285335163, "url", false, 12851303187790047985], [3722963349756955755, "once_cell", false, 15071223915464446217], [4405182208873388884, "http", false, 12093128787983855083], [5986029879202738730, "log", false, 5638320630720732856], [7414427314941361239, "hyper", false, 6773323531978413031], [7620660491849607393, "futures_core", false, 11290189045161448346], [8405603588346937335, "winreg", false, 1759275120245861134], [8915503303801890683, "http_body", false, 17921686113063823236], [9538054652646069845, "tokio", false, 15305522293980401147], [9689903380558560274, "serde", false, 10977844928489340561], [10229185211513642314, "mime", false, 799632151728794554], [10629569228670356391, "futures_util", false, 8240521222716982159], [11295624341523567602, "rustls", false, 14727300914968823642], [12186126227181294540, "tokio_native_tls", false, 17787011813262349703], [12367227501898450486, "hyper_tls", false, 8844948209865612159], [13809605890706463735, "h2", false, 6847113657553582524], [14564311161534545801, "encoding_rs", false, 9995490080141555987], [15367738274754116744, "serde_json", false, 17974425630600201351], [16066129441945555748, "bytes", false, 4498305474353292168], [16311359161338405624, "rustls_pemfile", false, 8037251016955283365], [16542808166767769916, "serde_urlencoded", false, 5617567085289434712], [16622232390123975175, "tokio_rustls", false, 14125158231104626606], [16785601910559813697, "native_tls_crate", false, 2198943927764683685], [17652733826348741533, "webpki_roots", false, 11451606823303959196], [17973378407174338648, "cookie_store", false, 15345925327042237716], [18066890886671768183, "base64", false, 11446804473527071446], [18071510856783138481, "mime_guess", false, 15533602699685303278]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-9a30642a1ef30402\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}