{"rustc": 12488743700189009532, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 8276155916380437441, "path": 13067367211683519187, "deps": [[784494742817713399, "tower_service", false, 14713849103421297527], [1906322745568073236, "pin_project_lite", false, 15636383175531162806], [2517136641825875337, "sync_wrapper", false, 13923617165680171792], [7712452662827335977, "tower_layer", false, 17148032096633824894], [7858942147296547339, "rustversion", false, 1948865793622661436], [8606274917505247608, "tracing", false, 16757813058469743714], [9010263965687315507, "http", false, 14438606696231830319], [10229185211513642314, "mime", false, 799632151728794554], [10629569228670356391, "futures_util", false, 8240521222716982159], [11946729385090170470, "async_trait", false, 6969772722108267470], [14084095096285906100, "http_body", false, 7124342440318254419], [16066129441945555748, "bytes", false, 4498305474353292168], [16900715236047033623, "http_body_util", false, 15475171450182443256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-8afc90b275b3c549\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}