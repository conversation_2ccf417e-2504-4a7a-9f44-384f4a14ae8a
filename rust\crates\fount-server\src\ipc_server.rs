//! IPC服务器
//! 对应原文件: src/server/ipc_server.mjs

use interprocess::local_socket::{LocalSocketListener, LocalSocketStream};
use std::io::{Read, Write};
use serde::{Deserialize, Serialize};
use anyhow::Result;
use std::path::Path;

/// IPC消息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IpcMessage {
    pub id: String,
    pub method: String,
    pub params: serde_json::Value,
}

/// IPC响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcResponse {
    pub id: String,
    pub result: Option<serde_json::Value>,
    pub error: Option<String>,
}

/// IPC服务器
pub struct IpcServer {
    socket_path: String,
}

impl IpcServer {
    /// 创建新的IPC服务器
    pub fn new(socket_path: impl Into<String>) -> Self {
        Self {
            socket_path: socket_path.into(),
        }
    }
    
    /// 启动IPC服务器
    pub async fn start(&self) -> Result<()> {
        // 清理旧的socket文件
        if Path::new(&self.socket_path).exists() {
            std::fs::remove_file(&self.socket_path)?;
        }
        
        let listener = LocalSocketListener::bind(self.socket_path.clone())?;
        tracing::info!("IPC server listening on {}", self.socket_path);
        
        loop {
            match listener.accept() {
                Ok(stream) => {
                    tokio::spawn(async move {
                        if let Err(e) = handle_client(stream).await {
                            tracing::error!("IPC client error: {}", e);
                        }
                    });
                }
                Err(e) => {
                    tracing::error!("Failed to accept IPC connection: {}", e);
                }
            }
        }
    }
}

/// 处理IPC客户端连接
async fn handle_client(mut stream: LocalSocketStream) -> Result<()> {
    let mut buffer = vec![0u8; 4096];
    
    loop {
        let n = stream.read(&mut buffer)?;
        if n == 0 {
            break;
        }

        let message_str = String::from_utf8_lossy(&buffer[..n]);

        match serde_json::from_str::<IpcMessage>(&message_str) {
            Ok(message) => {
                let response = process_message(message).await;
                let response_str = serde_json::to_string(&response)?;
                stream.write_all(response_str.as_bytes())?;
            }
            Err(e) => {
                tracing::error!("Failed to parse IPC message: {}", e);
                let error_response = IpcResponse {
                    id: "unknown".to_string(),
                    result: None,
                    error: Some(format!("Parse error: {}", e)),
                };
                let response_str = serde_json::to_string(&error_response)?;
                stream.write_all(response_str.as_bytes())?;
            }
        }
    }
    
    Ok(())
}

/// 处理IPC消息
async fn process_message(message: IpcMessage) -> IpcResponse {
    match message.method.as_str() {
        "ping" => IpcResponse {
            id: message.id,
            result: Some(serde_json::json!("pong")),
            error: None,
        },
        "get_status" => IpcResponse {
            id: message.id,
            result: Some(serde_json::json!({
                "status": "running",
                "uptime": 0
            })),
            error: None,
        },
        _ => IpcResponse {
            id: message.id,
            result: None,
            error: Some(format!("Unknown method: {}", message.method)),
        },
    }
}
