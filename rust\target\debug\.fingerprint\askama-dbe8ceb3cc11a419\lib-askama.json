{"rustc": 12488743700189009532, "features": "[\"config\", \"default\", \"dep_humansize\", \"dep_num_traits\", \"humansize\", \"mime\", \"mime_guess\", \"num-traits\", \"percent-encoding\", \"urlencode\", \"with-axum\"]", "declared_features": "[\"comrak\", \"config\", \"default\", \"dep_humansize\", \"dep_num_traits\", \"humansize\", \"markdown\", \"mime\", \"mime_guess\", \"num-traits\", \"percent-encoding\", \"serde\", \"serde-json\", \"serde-yaml\", \"serde_json\", \"serde_yaml\", \"urlencode\", \"with-actix-web\", \"with-axum\", \"with-gotham\", \"with-hyper\", \"with-mendes\", \"with-rocket\", \"with-tide\", \"with-warp\"]", "target": 13506025875297133661, "profile": 8276155916380437441, "path": 7888821292479115785, "deps": [[40386456601120721, "percent_encoding", false, 11201700476752682152], [5157631553186200874, "dep_num_traits", false, 2356961692299886881], [6593674146359544692, "dep_humansize", false, 4755650579125387752], [7209081853818273543, "askama_derive", false, 10276156110749111060], [13006376796596721141, "askama_escape", false, 8302785648216326375]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\askama-dbe8ceb3cc11a419\\dep-lib-askama", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}