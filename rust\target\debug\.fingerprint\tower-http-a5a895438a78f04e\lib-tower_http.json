{"rustc": 12488743700189009532, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 8276155916380437441, "path": 9958066978812107341, "deps": [[784494742817713399, "tower_service", false, 14713849103421297527], [1906322745568073236, "pin_project_lite", false, 15636383175531162806], [4121350475192885151, "iri_string", false, 571470815375274107], [5695049318159433696, "tower", false, 2024279504235928808], [7712452662827335977, "tower_layer", false, 17148032096633824894], [7896293946984509699, "bitflags", false, 18003315745341663165], [9010263965687315507, "http", false, 14438606696231830319], [10629569228670356391, "futures_util", false, 8240521222716982159], [14084095096285906100, "http_body", false, 7124342440318254419], [16066129441945555748, "bytes", false, 4498305474353292168]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-a5a895438a78f04e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}