{"rustc": 12488743700189009532, "features": "[\"c_ffi\", \"default\", \"rust_backend\"]", "declared_features": "[\"c_ffi\", \"cfzlib\", \"default\", \"ngzlib\", \"rust_backend\"]", "target": 5164817970457834428, "profile": 8276155916380437441, "path": 15443651548770498381, "deps": [[781484216458329418, "rgb", false, 231323808593688911], [2924422107542798392, "libc", false, 1728529705282482843], [5466618496199522463, "crc32fast", false, 8230313352151936750], [10563170702865159712, "flate2", false, 5651241975934006812]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lodepng-d4ca0f6d8279fea4\\dep-lib-lodepng", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}