{"rustc": 12488743700189009532, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 7872416515397446104, "deps": [[11033263105862272874, "tracing_core", false, 10485554372642425387]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-dad6f54c2b957c81\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}