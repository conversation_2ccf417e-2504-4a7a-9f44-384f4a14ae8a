{"rustc": 12488743700189009532, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 8276155916380437441, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 2742212932505749019], [442785307232013896, "tauri_runtime", false, 14500700714929902453], [3150220818285335163, "url", false, 10260461479629395240], [3722963349756955755, "once_cell", false, 15071223915464446217], [4143744114649553716, "raw_window_handle", false, 8264662274193606652], [5986029879202738730, "log", false, 7320443587293871305], [7752760652095876438, "build_script_build", false, 11065239835089946768], [8539587424388551196, "webview2_com", false, 2260269201761289649], [9010263965687315507, "http", false, 14438606696231830319], [11050281405049894993, "tauri_utils", false, 2262565235679204269], [13116089016666501665, "windows", false, 15799024179589216305], [13223659721939363523, "tao", false, 13267541617441655312], [14794439852947137341, "wry", false, 5540396627655272377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-5bd5a734c714a4ce\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}