{"rustc": 12488743700189009532, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 8276155916380437441, "path": 15532070880719131931, "deps": [[1906322745568073236, "pin_project_lite", false, 15636383175531162806], [3129130049864710036, "memchr", false, 15731388004123817441], [7620660491849607393, "futures_core", false, 11290189045161448346], [9538054652646069845, "tokio", false, 15305522293980401147], [9556762810601084293, "brotli", false, 12859348976657119124], [10563170702865159712, "flate2", false, 5651241975934006812]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-d16fc307d41a3133\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}