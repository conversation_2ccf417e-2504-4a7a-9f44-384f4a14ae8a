{"rustc": 12488743700189009532, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 7872416515397446104, "deps": [[1009387600818341822, "matchers", false, 4086176834935005957], [1017461770342116999, "sharded_slab", false, 13927171296628315626], [3722963349756955755, "once_cell", false, 15071223915464446217], [6048213226671835012, "smallvec", false, 10920917952552551199], [6981130804689348050, "tracing_serde", false, 16479386892026748995], [8606274917505247608, "tracing", false, 16757813058469743714], [8614575489689151157, "nu_ansi_term", false, 397245382275658419], [9451456094439810778, "regex", false, 12250754408636079168], [9689903380558560274, "serde", false, 10977844928489340561], [10806489435541507125, "tracing_log", false, 11475208164380646649], [11033263105862272874, "tracing_core", false, 10485554372642425387], [12427285511609802057, "thread_local", false, 11402667918343055202], [15367738274754116744, "serde_json", false, 10645753758292327481]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-84f8127d872ed27e\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}