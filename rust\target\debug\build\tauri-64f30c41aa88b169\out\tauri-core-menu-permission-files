["\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-64f30c41aa88b169\\out\\permissions\\menu\\autogenerated\\default.toml"]