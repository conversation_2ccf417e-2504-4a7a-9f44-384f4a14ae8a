{"rustc": 12488743700189009532, "features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-util\", \"tracing\", \"util\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 8276155916380437441, "path": 7756936533198226835, "deps": [[784494742817713399, "tower_service", false, 14713849103421297527], [1288403060204016458, "tokio_util", false, 9453835930420776786], [1906322745568073236, "pin_project_lite", false, 15636383175531162806], [6264115378959545688, "pin_project", false, 11399026028730934973], [6955678925937229351, "slab", false, 10302835216270481514], [7620660491849607393, "futures_core", false, 11290189045161448346], [7712452662827335977, "tower_layer", false, 17148032096633824894], [8153389937262086537, "hdrhistogram", false, 17668843252081566521], [8606274917505247608, "tracing", false, 16757813058469743714], [9538054652646069845, "tokio", false, 15305522293980401147], [10629569228670356391, "futures_util", false, 8240521222716982159], [13208667028893622512, "rand", false, 9557639272206221063], [14923790796823607459, "indexmap", false, 17866685744806585863]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-e2eab1ba6f2d4230\\dep-lib-tower", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}