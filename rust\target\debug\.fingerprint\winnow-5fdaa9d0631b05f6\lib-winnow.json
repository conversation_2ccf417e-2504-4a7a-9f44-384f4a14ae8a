{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 11259629673856435433, "path": 1966853738571242711, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-5fdaa9d0631b05f6\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}